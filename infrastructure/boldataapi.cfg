# Most of the configuration is provided by BOLDATAAPI_{section}_{option} environment variables

[main]
docker_tag = DOCKER_TAG

#
# Logging configuration
# https://docs.python.org/3/library/logging.config.html#logging-config-fileformat

[loggers]
keys = root, bda_access

[logger_root]
handlers = console
level = INFO

[logger_bda_access]
qualname = bda-access
handlers = bda_access
propagate = 1

[handlers]
keys = console, bda_access

[handler_console]
class = StreamHandler
args = (sys.stdout,)
formatter = default
level = INFO

[handler_bda_access]
class = StreamHandler
args = (sys.stdout,)
formatter = bda_access

[formatters]
keys = default, bda_access

[formatter_default]
class = boldataapi.logging.JsonFormatter
format =
    [
        "asctime", "levelname", {
            "message": "message",
            "pid": "process",
            "thread": "threadName",
            "path": "pathname",
            "line": "lineno",
            "func": "funcName"
        }
    ]

[formatter_bda_access]
# This one already gets formatted JSON, although if we wished to, we could
# re-format it ourselves
class = boldataapi.logging.Formatter
format = %(message)s
