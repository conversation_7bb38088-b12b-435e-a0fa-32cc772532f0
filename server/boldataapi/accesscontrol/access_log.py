# Copied from https://git.vaultit.org/qvarn/qvarn/blob/master/qvarn/access_log.py

import datetime
import json
import re

import jwt

from boldataapi.accesscontrol.token_reader import ALGORITHMS


QVARN_ID_OR_UUID_RE = re.compile(
    '^(?:'
    '[0-9a-fA-F]{4}-[0-9a-fA-F]{32}-[0-9a-fA-F]{8}'
    '|'
    '[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}'
    ')$'
)


class AccessLogger(object):

    def __init__(self, logger, entry_chunk_size=None):
        self.logger = logger
        self.entry_chunk_size = entry_chunk_size or 300

    def log_access(self, request, data):
        rtype = self._get_rtype(request, data)
        op = self._get_op(request)

        ids, revision = self._get_ids_and_revision(request, data)
        if not ids:
            # we might be returning response that doesn't look like
            # a resource or a resource list (/version is one example)
            # can't do anything about it here.
            return

        ahead = request.get_header('Authorization', '') or ''
        qhead = request.get_header('Qvarn-Token', '') or ''
        ohead = request.get_header('Qvarn-Access-By', '') or ''
        whead = request.get_header('Qvarn-Why', None)

        token_headers = ahead
        if qhead:
            token_headers = ', '.join([ahead, qhead])
        encoded_tokens = re.split(r'(?:\A|,\s*)Bearer ', token_headers or '')[1:]
        tokens = [
            jwt.decode(
                t,
                options={'verify_signature': False},
                algorithms=ALGORITHMS
            )
            for t in encoded_tokens
        ]

        persons = [
            {
                'accessor_id': t['sub'],
                'accessor_type': 'person',
            }
            for t in tokens]
        clients = [
            {
                'accessor_id': t['aud'],
                'accessor_type': 'client',
            }
            for t in tokens]
        orgs = [
            {
                'accessor_id': t,
                'accessor_type': 'org',
            }
            for t in re.findall(r',?\s*Org (.+?)(?:,|\Z)', ohead) if t]
        others = [
            {
                'accessor_id': t,
                'accessor_type': 'other',
            }
            for t in re.findall(r',?\s*Other (.+?)(?:,|\Z)', ohead) if t]

        ip_address = request.headers.get('X-Forwarded-For')

        for some_ids in self._split(self.entry_chunk_size, ids):
            # room for improvement: replace `msg_type `with custom log level ACCESS:
            # https://vaultit.atlassian.net/wiki/spaces/AT/pages/926547986/Kubernetes+and+centralized+access+logging
            entry = {
                'msg_type': 'access_log',
                'operation': op,
                'resource_type': rtype,
                'resource_ids': some_ids,
                'resource_revision': revision,
                'accessors': persons + clients + orgs + others,
                'reason': whead,
                'ip_address': ip_address,
                'timestamp': datetime.datetime.utcnow().isoformat() + 'Z',
            }
            # Filter out None and complex types for extra dict to avoid Azure AI warnings
            # Azure AI only accepts primitive types: bool, str, bytes, int, float

            def is_primitive(value):
                return isinstance(value, (bool, str, bytes, int, float))

            extra_entry = {k: v for k, v in entry.items() if is_primitive(v)}
            self.logger.info('%s', json.dumps(entry), extra=extra_entry)

    def _get_ids_and_revision(self, request, data):
        if not data or not isinstance(data, dict):
            # In case we are returning a file or it's a delete request
            # extract external_id from path for BDA models
            path_parts = getattr(request, 'path', '').split('/')
            if len(path_parts) > 2 and QVARN_ID_OR_UUID_RE.match(path_parts[2]):
                ids = [path_parts[2]]
            else:
                ids = []
            revision = None
        else:
            # Prefer 'external_id' for BDA models, fallback to 'id'
            ids = []
            revision = None
            # Check for any top-level list of dicts (BDA: e.g. 'projects', 'suppliers', etc.)
            for key, value in data.items():
                if isinstance(value, list) and value and all(isinstance(r, dict) for r in value):
                    ids = [
                        r.get('external_id') or r.get('id')
                        for r in value
                        if r.get('external_id') or r.get('id')
                    ]
                    break
            # Fallback to 'resources' key for backward compatibility
            if not ids and 'resources' in data and isinstance(data.get('resources'), list):
                ids = [
                    r.get('external_id') or r.get('id')
                    for r in data['resources']
                    if isinstance(r, dict) and (r.get('external_id') or r.get('id'))
                ]
            # Single resource object
            if not ids and (data.get('external_id') or data.get('id')):
                ids = [data.get('external_id') or data.get('id')]
                revision = data.get('revision')
        # make the logging deterministic
        ids.sort()
        return ids, revision

    def _get_rtype(self, request, data):
        # If data has `resource_type` field, we use it as resource type
        if isinstance(data, dict) and data.get('resource_type'):
            return data['resource_type']

        # Otherwise we try to guess it from request URL.
        path_parts = getattr(request, 'path', '').split('/')
        if len(path_parts) >= 2:
            rpart = path_parts[1]
            return rpart[:-1] if rpart and rpart[-1] == 's' else rpart
        return None

    def _get_op(self, request):
        method = getattr(request, 'method', None)
        if method == 'GET':
            path_parts = getattr(request, 'path', '').split('/')
            if len(path_parts) == 2:
                return 'LIST'
            elif 'search' in path_parts:
                return 'SEARCH'
            else:
                return 'GET'
        else:
            return method

    def _split(self, n, ids):
        while len(ids) > n:
            yield ids[:n]
            ids = ids[n:]
        yield ids
