import json
import logging
from datetime import datetime

import jwt
from freezegun import freeze_time

from boldataapi.accesscontrol.access_log import AccessLogger
from boldataapi.fixtures import factories

API_URL = '/api/v1/boldata/'


def get_logs(caplog):
    log = ''
    for r in caplog.records:
        time = datetime.fromtimestamp(r.created).isoformat()
        log += f"[{r.levelno}.{r.levelname}] {r.message} {time}"
        if r.stack_info is not None:
            log += f'\n{r.stack_info}'
        log += "\n"
    return log


def make_token():
    return jwt.encode({
        'sub': 'le subject',
        'aud': 'ze client',
    }, key="secret", algorithm="HS256")


def assert_no_warnings(caplog):
    for record in caplog.records:
        assert record.levelno < logging.WARNING, (
            f"Unexpected warning or error log: {record.message}"
        )


@freeze_time('2020-11-11')
def test_get_list_log(config, app, db, caplog):
    caplog.set_level(logging.INFO)
    p1 = factories.create_project(db)
    p2 = factories.create_project(db)

    resp = app.get(API_URL + 'projects', headers={
        'Qvarn-why': "for important reasons",
        'Authorization': f"Bearer {make_token()}",
    })
    assert 200 == resp.status_code

    expected = (
        '[20.INFO] Getting all project ids 2020-11-11T00:00:00'
        '\n'
        '[20.INFO] {'
        '"msg_type": "access_log", '
        '"operation": "LIST", '
        '"resource_type": "project", '
        '"resource_ids": ["%s", "%s"], '
        '"resource_revision": null, '
        '"accessors": ['
        '{"accessor_id": "le subject",'
        ' "accessor_type": "person"}, '
        '{"accessor_id": "ze client",'
        ' "accessor_type": "client"}], '
        '"reason": "for important reasons", '
        '"ip_address": null, '
        '"timestamp": "2020-11-11T00:00:00Z"} '
        '2020-11-11T00:00:00'
        '\n'
        % tuple(sorted([p1['external_id'], p2['external_id']]))
    )

    assert expected == get_logs(caplog)
    assert_no_warnings(caplog)


@freeze_time('2020-11-11')
def test_get_one_log(config, app, db, caplog):
    caplog.set_level(logging.INFO)
    p = factories.create_project(db)

    resp = app.get(API_URL + 'projects/' + p['external_id'])
    assert 200 == resp.status_code

    expected = (
        '[20.INFO] Getting project %s 2020-11-11T00:00:00'
        '\n'
        '[20.INFO] {'
        '"msg_type": "access_log", '
        '"operation": "GET", '
        '"resource_type": "project", '
        '"resource_ids": ["%s"], '
        '"resource_revision": null, '
        '"accessors": [], '
        '"reason": null, '
        '"ip_address": null, '
        '"timestamp": "2020-11-11T00:00:00Z"} '
        '2020-11-11T00:00:00'
        '\n'
        % (p["external_id"], p["external_id"])
    )

    assert expected == get_logs(caplog)
    assert_no_warnings(caplog)


@freeze_time('2020-11-11')
def test_post_log(config, app, db, caplog):
    caplog.set_level(logging.INFO)

    project_ids = []

    project = {
        'names': ['Test project'],
        'project_responsible_org': 'client-comp-id',
        'start_date': '2020-01-01',
        'end_date': '2020-12-01',
        'state': 'draft',
        'project_ids': project_ids,
        'project_responsible_person': None,
    }
    resp = app.post_json(API_URL + 'projects', project)
    p = resp.json
    assert 201 == resp.status_code

    expected = (
        '[20.INFO] Creating a new project 2020-11-11T00:00:00'
        '\n'
        '[20.INFO] New project created %s 2020-11-11T00:00:00'
        '\n'
        '[20.INFO] {'
        '"msg_type": "access_log", '
        '"operation": "POST", '
        '"resource_type": "project", '
        '"resource_ids": ["%s"], '
        '"resource_revision": null, '
        '"accessors": [], '
        '"reason": null, '
        '"ip_address": null, '
        '"timestamp": "2020-11-11T00:00:00Z"} '
        '2020-11-11T00:00:00'
        '\n'
        % (p["id"], p["id"])
    )

    assert expected == get_logs(caplog)
    assert_no_warnings(caplog)


@freeze_time('2020-11-11')
def test_put_log(config, app, db, caplog):
    caplog.set_level(logging.INFO)

    project = factories.create_project(db)
    resp = app.put_json(
        API_URL + 'projects/%s' % project['external_id'],
        {
            'names': ['Updated project name'],
        }
    )
    assert 200 == resp.status_code
    p = resp.json

    expected = (
        '[20.INFO] Updating project %s 2020-11-11T00:00:00'
        '\n'
        '[20.INFO] {'
        '"msg_type": "access_log", '
        '"operation": "PUT", '
        '"resource_type": "project", '
        '"resource_ids": ["%s"], '
        '"resource_revision": null, '
        '"accessors": [], '
        '"reason": null, '
        '"ip_address": null, '
        '"timestamp": "2020-11-11T00:00:00Z"} '
        '2020-11-11T00:00:00'
        '\n'
        % (p["id"], p["id"])
    )

    assert expected == get_logs(caplog)
    assert_no_warnings(caplog)


@freeze_time('2020-11-11')
def test_delete_log(config, app, db, caplog):
    caplog.set_level(logging.INFO)

    p = factories.create_project(db)

    resp = app.delete(API_URL + 'projects/' + p['external_id'])
    assert 200 == resp.status_code

    expected = (
        '[20.INFO] Deleting project %s 2020-11-11T00:00:00'
        '\n'
        '[20.INFO] {'
        '"msg_type": "access_log", '
        '"operation": "DELETE", '
        '"resource_type": "project", '
        '"resource_ids": ["%s"], '
        '"resource_revision": null, '
        '"accessors": [], '
        '"reason": null, '
        '"ip_address": null, '
        '"timestamp": "2020-11-11T00:00:00Z"} '
        '2020-11-11T00:00:00'
        '\n'
        % (p["external_id"], p["external_id"])
    )

    assert expected == get_logs(caplog)
    assert_no_warnings(caplog)


@freeze_time('2020-11-11')
def test_bda_list_resource_key(caplog):
    caplog.set_level(logging.INFO)
    # Simulate a response with a top-level 'suppliers' list
    data = {
        'suppliers': [
            {'external_id': 'sup1'},
            {'external_id': 'sup2'},
        ]
    }
    logger = logging.getLogger('bda-access')
    access_logger = AccessLogger(logger)

    class DummyRequest:
        path = '/api/v1/boldata/suppliers'
        method = 'GET'
        headers = {}

        def get_header(self, name, default=None):
            return default

    access_logger.log_access(DummyRequest(), data)
    logs = [
        r for r in caplog.records if r.levelno == logging.INFO and 'access_log' in r.message
    ]
    assert logs, 'No access log emitted for suppliers list'
    assert set(json.loads(logs[0].message)['resource_ids']) == {'sup1', 'sup2'}
    assert_no_warnings(caplog)


@freeze_time('2020-11-11')
def test_bda_single_resource_external_id(caplog):
    caplog.set_level(logging.INFO)
    data = {'external_id': 'sup1', 'revision': 'r1'}
    logger = logging.getLogger('bda-access')
    access_logger = AccessLogger(logger)

    class DummyRequest:
        path = '/api/v1/boldata/suppliers/sup1'
        method = 'GET'
        headers = {}

        def get_header(self, name, default=None):
            return default

    access_logger.log_access(DummyRequest(), data)
    logs = [
        r for r in caplog.records if r.levelno == logging.INFO and 'access_log' in r.message
    ]
    assert logs, 'No access log emitted for single supplier'
    assert json.loads(logs[0].message)['resource_ids'] == ['sup1']
    assert json.loads(logs[0].message)['resource_revision'] == 'r1'
    assert_no_warnings(caplog)


@freeze_time('2020-11-11')
def test_bda_single_resource_id_fallback(caplog):
    caplog.set_level(logging.INFO)
    data = {'id': 'fallbackid'}
    logger = logging.getLogger('bda-access')
    access_logger = AccessLogger(logger)

    class DummyRequest:
        path = '/api/v1/boldata/suppliers/fallbackid'
        method = 'GET'
        headers = {}

        def get_header(self, name, default=None):
            return default

    access_logger.log_access(DummyRequest(), data)
    logs = [
        r for r in caplog.records if r.levelno == logging.INFO and 'access_log' in r.message
    ]
    assert logs, 'No access log emitted for id fallback'
    assert json.loads(logs[0].message)['resource_ids'] == ['fallbackid']
    assert_no_warnings(caplog)


@freeze_time('2020-11-11')
def test_bda_resources_key_compatibility(caplog):
    caplog.set_level(logging.INFO)
    data = {
        'resources': [
            {'external_id': 'r1'},
            {'id': 'r2'},
        ]
    }
    logger = logging.getLogger('bda-access')
    access_logger = AccessLogger(logger)

    class DummyRequest:
        path = '/api/v1/boldata/reports'
        method = 'GET'
        headers = {}

        def get_header(self, name, default=None):
            return None

    access_logger.log_access(DummyRequest(), data)
    logs = [
        r for r in caplog.records if r.levelno == logging.INFO and 'access_log' in r.message
    ]
    assert logs, 'No access log emitted for resources key'
    assert set(json.loads(logs[0].message)['resource_ids']) == {'r1', 'r2'}
    assert_no_warnings(caplog)


@freeze_time('2020-11-11')
def test_bda_no_ids_no_log(caplog):
    caplog.set_level(logging.INFO)
    data = {'foo': 'bar'}
    logger = logging.getLogger('bda-access')
    access_logger = AccessLogger(logger)

    class DummyRequest:
        path = '/api/v1/boldata/suppliers'
        method = 'GET'
        headers = {}

        def get_header(self, name, default=None):
            return None

    access_logger.log_access(DummyRequest(), data)
    logs = [
        r for r in caplog.records if r.levelno == logging.INFO and 'access_log' in r.message
    ]
    assert not logs, 'Access log should not be emitted if no ids found'
    assert_no_warnings(caplog)


@freeze_time('2020-11-11')
def test_none_values_filtered_from_extra(caplog):
    caplog.set_level(logging.INFO)
    data = {'external_id': 'sup1'}
    logger = logging.getLogger('bda-access')
    access_logger = AccessLogger(logger)

    class DummyRequest:
        path = '/api/v1/boldata/suppliers/sup1'
        method = 'GET'
        headers = {}

        def get_header(self, name, default=None):
            return default

    access_logger.log_access(DummyRequest(), data)
    logs = [
        r for r in caplog.records if r.levelno == logging.INFO and 'access_log' in r.message
    ]
    assert logs, 'Access log should be emitted'

    # Check that JSON message contains null values
    log_entry = json.loads(logs[0].message)
    assert log_entry['resource_revision'] is None
    assert log_entry['reason'] is None
    assert log_entry['ip_address'] is None

    # Check that extra dict does not contain None values or complex types
    record = logs[0]
    extra_attrs = ['resource_revision', 'reason', 'ip_address', 'accessors', 'resource_ids']

    # The extra dict should not have keys with None values
    for attr in extra_attrs:
        if hasattr(record, attr):
            value = getattr(record, attr)
            if attr in ['resource_revision', 'reason', 'ip_address']:
                assert value is not None, f"Extra dict should not contain None for {attr}"
            # The extra dict should not contain complex types like lists or dicts
            assert not isinstance(value, (list, dict)), f"Complex type in extra for {attr}"

    assert_no_warnings(caplog)
